<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轻断食 App 页面原型 (流程优化版)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <style>
        body {
            font-family: 'Inter', 'Helvetica Neue', 'Hiragino Sans GB', 'WenQuanYi Micro Hei', 'Microsoft Yahei', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .screen-container {
            max-width: 420px; /* 模拟手机屏幕宽度 */
            min-height: 800px;
            margin: 1rem auto;
            border: 8px solid #1a202c;
            border-radius: 40px;
            box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
            overflow: hidden;
            position: relative;
        }
        .screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            pointer-events: none;
            overflow-y: auto;
            background-color: #f8fafc;
        }
        .screen.active {
            opacity: 1;
            pointer-events: auto;
        }
        .screen::-webkit-scrollbar { width: 4px; }
        .screen::-webkit-scrollbar-thumb { background: #cbd5e1; border-radius: 2px; }
        .screen::-webkit-scrollbar-track { background: transparent; }
        .nav-button.active { background-color: #10b981; color: white; font-weight: 600; }
        /* Modal styles */
        .modal-overlay {
            position: absolute;
            inset: 0;
            background-color: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: flex-end;
            justify-content: center;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease-in-out;
        }
        .modal-overlay.active {
            opacity: 1;
            pointer-events: auto;
        }
        .modal-content {
            background-color: white;
            width: 100%;
            border-radius: 20px 20px 0 0;
            padding: 1rem;
            transform: translateY(100%);
            transition: transform 0.3s ease-in-out;
        }
        .modal-overlay.active .modal-content {
            transform: translateY(0);
        }
    </style>
</head>
<body class="bg-gray-100">

    <div class="p-4 text-center">
        <h1 class="text-2xl font-bold text-gray-800">轻断食App页面原型 (流程优化版)</h1>
        <p class="text-gray-600 mt-2">已按新流程优化。请点击下方按钮切换页面。</p>
    </div>

    <!-- 页面切换导航 -->
    <div class="flex flex-wrap justify-center gap-2 p-4 max-w-4xl mx-auto">
        <button class="nav-button px-3 py-1.5 text-xs md:px-4 md:py-2 md:text-sm bg-white rounded-full shadow-sm" onclick="showScreen('intro')">1. 服务介绍</button>
        <button class="nav-button px-3 py-1.5 text-xs md:px-4 md:py-2 md:text-sm bg-white rounded-full shadow-sm" onclick="showScreen('health-form')">2. 健康信息</button>
        <button class="nav-button px-3 py-1.5 text-xs md:px-4 md:py-2 md:text-sm bg-white rounded-full shadow-sm" onclick="showScreen('product-selection')">3. 产品选购</button>
        <button class="nav-button px-3 py-1.5 text-xs md:px-4 md:py-2 md:text-sm bg-white rounded-full shadow-sm" onclick="showScreen('payment')">4. 付款页面</button>
        <button class="nav-button px-3 py-1.5 text-xs md:px-4 md:py-2 md:text-sm bg-white rounded-full shadow-sm" onclick="showScreen('payment-loading')">5. 支付处理</button>
        <button class="nav-button px-3 py-1.5 text-xs md:px-4 md:py-2 md:text-sm bg-white rounded-full shadow-sm" onclick="showScreen('payment-success')">6. 支付完成</button>
        <button class="nav-button px-3 py-1.5 text-xs md:px-4 md:py-2 md:text-sm bg-white rounded-full shadow-sm" onclick="showScreen('weight-info')">7. 开始轻断食</button>
        <button class="nav-button px-3 py-1.5 text-xs md:px-4 md:py-2 md:text-sm bg-white rounded-full shadow-sm" onclick="showScreen('dashboard')">8. 每日任务</button>
        <button class="nav-button px-3 py-1.5 text-xs md:px-4 md:py-2 md:text-sm bg-white rounded-full shadow-sm" onclick="showScreen('results')">9. 成果总结</button>
        <button class="nav-button px-3 py-1.5 text-xs md:px-4 md:py-2 md:text-sm bg-white rounded-full shadow-sm" onclick="showScreen('recovery-phase')">10. 复食阶段</button>
        <button class="nav-button px-3 py-1.5 text-xs md:px-4 md:py-2 md:text-sm bg-white rounded-full shadow-sm" onclick="showScreen('recovery-complete')">11. 复食结束</button>
    </div>

    <!-- 每日切换导航 -->
    <div id="daily-nav" class="flex justify-center gap-2 p-4 max-w-4xl mx-auto" style="display: none;">
        <div id="daily-buttons" class="flex gap-2">
            <!-- 动态生成每日按钮 -->
        </div>
    </div>

    <!-- 手机屏幕模拟容器 -->
    <div class="screen-container bg-white">
        <!-- 屏幕内容 -->

        <!-- 1. 服务介绍页 -->
        <div id="intro" class="screen">
            <div class="bg-emerald-50">
                <img src="https://placehold.co/420x280/a7f3d0/10b981?text=道家养生" alt="道家养生轻断食" class="w-full h-auto">
                <div class="p-6">
                    <h1 class="text-3xl font-bold text-emerald-900">道家养生 · 轻断食之旅</h1>
                    <p class="mt-2 text-emerald-700">一场身与心的净化，遇见更轻盈的自己</p>
                </div>
            </div>
            <div class="p-6 space-y-8">
                <div>
                    <h3 class="font-semibold text-gray-800 text-lg flex items-center"><i data-lucide="leaf" class="text-emerald-600 mr-2"></i>服务功效</h3>
                    <p class="mt-2 text-gray-600">通过科学的道家养生法，帮助您安全有效地进行身体清调，实现体重管理、改善亚健康、焕发身心活力。</p>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-800 text-lg flex items-center"><i data-lucide="package" class="text-emerald-600 mr-2"></i>配套产品包</h3>
                    <p class="mt-2 text-gray-600">我们为您准备了全套轻断食所需的产品，包括君仓清、艾灸贴、肝胆排毒液、丹道药茶等，均源自道医团队严选，确保品质与效果。</p>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-800 text-lg flex items-center"><i data-lucide="gem" class="text-emerald-600 mr-2"></i>服务价值</h3>
                    <p class="mt-2 text-gray-600">您将获得的不仅是体重上的变化，更是一套可长期受益的健康生活方式。全程AI健管师陪伴指导，让您的轻断食之旅安全、轻松、有效。</p>
                </div>
            </div>
            <div class="p-6 sticky bottom-0 bg-white border-t">
                <button class="w-full bg-emerald-600 text-white font-bold py-4 rounded-xl shadow-lg shadow-emerald-200 hover:bg-emerald-700 transition-all" onclick="showScreen('health-form')">
                    立即购买
                </button>
            </div>
        </div>

        <!-- 2. 健康信息采集 -->
        <div id="health-form" class="screen p-6">
            <div class="flex items-center mb-6">
                <button onclick="showScreen('intro')" class="p-2 rounded-full hover:bg-gray-100"><i data-lucide="arrow-left"></i></button>
                <h1 class="text-2xl font-bold text-gray-800 ml-4">基本健康信息</h1>
            </div>
            <p class="text-gray-600 mb-6">为了给您推荐最合适的方案，请填写真实信息。</p>
            <form class="space-y-6">
                <div>
                    <label class="font-medium text-gray-700">您的性别</label>
                    <div class="grid grid-cols-2 gap-4 mt-2">
                        <label class="flex items-center p-3 bg-gray-100 rounded-lg border-2 border-transparent has-[:checked]:border-emerald-500"><input type="radio" name="gender" class="h-5 w-5 accent-emerald-600"><span class="ml-3 text-gray-800">男</span></label>
                        <label class="flex items-center p-3 bg-gray-100 rounded-lg border-2 border-transparent has-[:checked]:border-emerald-500"><input type="radio" name="gender" class="h-5 w-5 accent-emerald-600"><span class="ml-3 text-gray-800">女</span></label>
                    </div>
                </div>
                <div>
                    <label class="font-medium text-gray-700">您是否有高血压？</label>
                    <div class="grid grid-cols-2 gap-4 mt-2">
                         <label class="flex items-center p-3 bg-gray-100 rounded-lg border-2 border-transparent has-[:checked]:border-emerald-500"><input type="radio" name="hypertension" class="h-5 w-5 accent-emerald-600"><span class="ml-3 text-gray-800">是</span></label>
                        <label class="flex items-center p-3 bg-gray-100 rounded-lg border-2 border-transparent has-[:checked]:border-emerald-500"><input type="radio" name="hypertension" class="h-5 w-5 accent-emerald-600"><span class="ml-3 text-gray-800">否</span></label>
                    </div>
                </div>
            </form>
             <div class="pt-8 pb-6 sticky bottom-0 bg-gray-50 -m-6 mt-12 px-6 border-t">
                <button class="w-full bg-emerald-600 text-white font-bold py-4 rounded-xl shadow-lg shadow-emerald-200 hover:bg-emerald-700 transition-all" onclick="showScreen('product-selection')">
                    下一步，选择产品包
                </button>
            </div>
        </div>

        <!-- 3. 产品选购 -->
        <div id="product-selection" class="screen p-6">
            <div class="flex items-center mb-6">
                <button onclick="showScreen('health-form')" class="p-2 rounded-full hover:bg-gray-100"><i data-lucide="arrow-left"></i></button>
                <h1 class="text-2xl font-bold text-gray-800 ml-4">选择您的方案</h1>
            </div>
            <div class="space-y-4">
                <h3 class="font-semibold text-gray-700 text-center">选择轻断食产品包</h3>

                <!-- 产品包切换器 -->
                <div class="relative bg-white rounded-xl shadow-sm overflow-hidden">
                    <!-- 切换指示器 -->
                    <div class="flex bg-gray-100 rounded-lg p-1 mb-4">
                        <button id="package-tab-2" class="flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all" onclick="switchPackage(2)">
                            2日产品包
                        </button>
                        <button id="package-tab-3" class="flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all bg-emerald-600 text-white" onclick="switchPackage(3)">
                            3日产品包
                        </button>
                    </div>

                    <!-- 产品包内容 -->
                    <div id="package-content" class="p-4">
                        <!-- 3日产品包（默认显示） -->
                        <div id="package-3-content" class="package-content">
                            <div class="text-center mb-4">
                                <h4 class="text-xl font-bold text-gray-800">3日轻断食产品包</h4>
                                <p class="text-sm text-gray-500 mt-1">深度清调，效果更佳</p>
                                <div class="flex items-center justify-center mt-2">
                                    <span class="text-2xl font-bold text-emerald-600">¥ 888</span>
                                    <span class="text-sm text-gray-400 line-through ml-2">¥ 1288</span>
                                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full ml-2">推荐</span>
                                </div>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h5 class="font-semibold text-blue-800 mb-3">产品清单：</h5>
                                <div class="grid grid-cols-2 gap-2 text-xs text-blue-700">
                                    <div>• 丹道药茶 3包</div>
                                    <div>• 陈桂苓茶 3包</div>
                                    <div>• 君仓清 3袋</div>
                                    <div>• 回春丹 1瓶</div>
                                    <div>• 滋阴丸 1瓶</div>
                                    <div>• 肝胆排毒液 12瓶</div>
                                </div>
                            </div>
                        </div>

                        <!-- 2日产品包 -->
                        <div id="package-2-content" class="package-content hidden">
                            <div class="text-center mb-4">
                                <h4 class="text-xl font-bold text-gray-800">2日轻断食产品包</h4>
                                <p class="text-sm text-gray-500 mt-1">快速体验，轻松入门</p>
                                <div class="flex items-center justify-center mt-2">
                                    <span class="text-2xl font-bold text-emerald-600">¥ 688</span>
                                    <span class="text-sm text-gray-400 line-through ml-2">¥ 888</span>
                                </div>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h5 class="font-semibold text-blue-800 mb-3">产品清单：</h5>
                                <div class="grid grid-cols-2 gap-2 text-xs text-blue-700">
                                    <div>• 丹道药茶 2包</div>
                                    <div>• 陈桂苓茶 2包</div>
                                    <div>• 君仓清 2袋</div>
                                    <div>• 回春丹 1瓶</div>
                                    <div>• 滋阴丸 1瓶</div>
                                    <div>• 肝胆排毒液 6瓶</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 隐藏的radio输入 -->
                <input type="radio" name="package" value="2" id="package-2-radio" class="hidden">
                <input type="radio" name="package" value="3" id="package-3-radio" class="hidden" checked>
            </div>
            <div class="mt-8 space-y-4">
                <h3 class="font-semibold text-gray-700">选购智能硬件 (可选)</h3>
                <label class="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm cursor-pointer">
                    <div>
                        <p class="font-bold text-gray-800">智能体脂秤</p>
                        <p class="text-sm text-gray-500">精准记录身体数据</p>
                    </div>
                    <div class="flex items-center">
                        <p class="font-bold text-md text-gray-700 mr-4">¥ 199</p>
                        <input type="checkbox" class="h-6 w-6 rounded-md text-emerald-600 focus:ring-emerald-500">
                    </div>
                </label>
                <label class="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm cursor-pointer">
                    <div>
                        <p class="font-bold text-gray-800">智能血压计</p>
                        <p class="text-sm text-gray-500">实时监测血压变化</p>
                    </div>
                    <div class="flex items-center">
                        <p class="font-bold text-md text-gray-700 mr-4">¥ 299</p>
                        <input type="checkbox" class="h-6 w-6 rounded-md text-emerald-600 focus:ring-emerald-500">
                    </div>
                </label>
            </div>



             <div class="pt-8 pb-6 sticky bottom-0 bg-gray-50 -m-6 mt-8 px-6 border-t">
                <div class="flex justify-between items-center mb-4">
                    <p class="text-gray-600">合计：</p>
                    <p id="total-price" class="text-2xl font-bold text-red-600">¥ 888</p>
                </div>
                <button id="purchase-btn" class="w-full bg-emerald-600 text-white font-bold py-4 rounded-xl shadow-lg shadow-emerald-200 hover:bg-emerald-700 transition-all" onclick="toggleModal('shipping-modal')">
                    去结算
                </button>
            </div>
        </div>

        <!-- 4. 付款页面 -->
        <div id="payment" class="screen p-6">
            <div class="flex items-center mb-6">
                <button onclick="showScreen('product-selection')" class="p-2 rounded-full hover:bg-gray-100"><i data-lucide="arrow-left"></i></button>
                <h1 class="text-2xl font-bold text-gray-800 ml-4">确认付款</h1>
            </div>

            <!-- 订单信息 -->
            <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                <h3 class="font-semibold text-gray-800 mb-3">订单信息</h3>
                <div id="order-summary" class="space-y-2">
                    <!-- 订单详情将通过JavaScript填充 -->
                </div>
                <div class="border-t pt-3 mt-3">
                    <div class="flex justify-between items-center">
                        <span class="font-semibold text-gray-800">合计金额</span>
                        <span id="payment-total" class="text-xl font-bold text-red-600">¥ 888</span>
                    </div>
                </div>
            </div>

            <!-- 收货信息 -->
            <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                <h3 class="font-semibold text-gray-800 mb-3">收货信息</h3>
                <div id="shipping-summary" class="text-sm text-gray-600">
                    <!-- 收货信息将通过JavaScript填充 -->
                </div>
            </div>

            <!-- 支付方式 -->
            <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                <h3 class="font-semibold text-gray-800 mb-3">支付方式</h3>
                <div class="space-y-3">
                    <label class="flex items-center p-3 bg-gray-50 rounded-lg cursor-pointer">
                        <input type="radio" name="payment-method" value="wechat" checked class="h-5 w-5 text-emerald-600">
                        <i data-lucide="smartphone" class="w-6 h-6 text-green-500 mx-3"></i>
                        <span class="text-gray-800">微信支付</span>
                    </label>
                    <label class="flex items-center p-3 bg-gray-50 rounded-lg cursor-pointer">
                        <input type="radio" name="payment-method" value="alipay" class="h-5 w-5 text-emerald-600">
                        <i data-lucide="credit-card" class="w-6 h-6 text-blue-500 mx-3"></i>
                        <span class="text-gray-800">支付宝</span>
                    </label>
                </div>
            </div>

            <div class="pt-8 pb-6 sticky bottom-0 bg-gray-50 -m-6 mt-8 px-6 border-t">
                <button class="w-full bg-emerald-600 text-white font-bold py-4 rounded-xl shadow-lg shadow-emerald-200 hover:bg-emerald-700 transition-all" onclick="processPayment()">
                    立即付款
                </button>
            </div>
        </div>

        <!-- 5. 支付Loading页面 -->
        <div id="payment-loading" class="screen p-6 flex flex-col justify-center items-center text-center">
            <div class="w-20 h-20 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-emerald-600"></div>
            </div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">正在处理支付...</h1>
            <p class="text-gray-600">请稍候，不要关闭页面</p>
        </div>

        <!-- 6. 支付完成页面 -->
        <div id="payment-success" class="screen p-6">
            <div class="text-center mb-8">
                <div class="w-20 h-20 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="check" class="w-10 h-10 text-emerald-600"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">恭喜您已完成支付！</h1>
                <p class="text-gray-600">您的轻断食产品正在准备发货</p>
            </div>

            <!-- 物流信息 -->
            <div class="bg-white p-5 rounded-xl shadow-sm space-y-4 mb-6">
                <div class="flex items-center justify-between">
                    <h3 class="font-semibold text-gray-800">物流信息</h3>
                    <span class="text-sm text-emerald-600">订单号: #202507280001</span>
                </div>
                <div class="flex items-center text-lg font-semibold text-emerald-700">
                    <i data-lucide="truck" class="w-6 h-6 mr-3"></i>
                    <div class="flex-1">
                        <p>正在备货中</p>
                        <p class="text-sm text-gray-500 font-normal">预计1-2个工作日发货</p>
                    </div>
                </div>
                <div class="border-t pt-4">
                    <p class="text-sm text-gray-600"><strong>收货地址：</strong><span id="delivery-address">北京市朝阳区xxx街道xxx号</span></p>
                    <p class="text-sm text-gray-600 mt-1"><strong>联系方式：</strong><span id="delivery-contact">张三 138****8888</span></p>
                </div>
            </div>

            <!-- 企业微信按钮 -->
            <div class="bg-blue-50 p-4 rounded-xl mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i data-lucide="message-circle" class="w-5 h-5 text-blue-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-blue-800">专业指导服务</h4>
                            <p class="text-sm text-blue-600">添加企业微信，获得专业答疑</p>
                        </div>
                    </div>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-all" onclick="addWechat()">
                        添加微信
                    </button>
                </div>
            </div>

            <div class="pt-8 pb-6 sticky bottom-0 bg-gray-50 -m-6 mt-8 px-6 border-t">
                <button class="w-full bg-emerald-600 text-white font-bold py-4 rounded-xl shadow-lg shadow-emerald-200 hover:bg-emerald-700 transition-all" onclick="checkProductReceived()">
                    开启轻断食之旅
                </button>
            </div>
        </div>
        
        <!-- 7. 开始轻断食页面 -->
        <div id="weight-info" class="screen p-6">
            <div class="text-center mb-8">
                <i data-lucide="calendar" class="w-16 h-16 text-emerald-500 mx-auto"></i>
                <h1 class="text-3xl font-bold text-gray-800 mt-4">规划您的轻断食计划</h1>
                <p class="text-gray-600 mt-2">请选择开始日期并记录您的初始体重</p>
            </div>

            <div class="space-y-6">
                <!-- 选择开始日期 -->
                <div class="bg-white p-5 rounded-xl shadow-sm">
                    <h3 class="font-semibold text-gray-800 mb-3">选择开始日期</h3>
                    <input type="date" id="start-date" class="w-full p-3 border border-gray-300 rounded-lg focus:border-emerald-500 focus:ring-emerald-500">
                </div>

                <!-- 显示轻断食日期范围 -->
                <div id="date-range-display" class="bg-emerald-50 p-5 rounded-xl border border-emerald-200" style="display: none;">
                    <h3 class="font-semibold text-emerald-800 mb-2">您的轻断食计划</h3>
                    <div class="text-sm text-emerald-700">
                        <p><strong>计划类型：</strong><span id="plan-type">3日轻断食</span></p>
                        <p><strong>开始日期：</strong><span id="display-start-date">2025-07-28</span></p>
                        <p><strong>结束日期：</strong><span id="display-end-date">2025-07-30</span></p>
                        <p class="mt-2 text-xs text-emerald-600">请确保在此期间有充足的时间进行轻断食</p>
                    </div>
                </div>

                <!-- 记录初始体重 -->
                <div class="bg-white p-5 rounded-xl shadow-sm">
                    <h3 class="font-semibold text-gray-800 mb-3">记录初始体重</h3>
                    <div class="flex items-center">
                        <input type="number" id="initial-weight" step="0.1" placeholder="例如: 65.5" class="flex-1 text-center text-xl font-bold p-3 bg-gray-100 rounded-lg border-transparent focus:border-emerald-500 focus:ring-emerald-500">
                        <span class="ml-3 text-gray-600 font-medium">kg</span>
                    </div>
                </div>
            </div>

            <div class="pt-8 pb-6 sticky bottom-0 bg-gray-50 -mx-6 mt-8 px-6 border-t">
                <button class="w-full bg-emerald-600 text-white font-bold py-4 rounded-xl shadow-lg shadow-emerald-200 hover:bg-emerald-700 transition-all" onclick="startFastingJourney()">
                    确认轻断食计划
                </button>
            </div>
        </div>

        <!-- 8. 最终体重记录页面 -->
        <div id="final-weight-info" class="screen p-6 flex flex-col justify-center items-center text-center">
            <i data-lucide="scale" class="w-16 h-16 text-emerald-500"></i>
            <h1 class="text-3xl font-bold text-gray-800 mt-4">记录您的最终体重</h1>
            <p class="text-gray-600 mt-2 mb-8">恭喜您完成轻断食！请记录您的最终体重，见证您的改变。</p>
            <div class="w-full max-w-xs">
                <label class="font-medium text-gray-700">最终体重 (kg)</label>
                <input type="number" id="final-weight" step="0.1" placeholder="例如: 63.2" class="w-full text-center text-2xl font-bold p-4 mt-2 bg-gray-100 rounded-lg border-transparent focus:border-emerald-500 focus:ring-emerald-500">
            </div>
            <div class="w-full pt-8 pb-6 absolute bottom-0 left-0 px-6 bg-gray-50 border-t">
                <button class="w-full bg-emerald-600 text-white font-bold py-4 rounded-xl shadow-lg shadow-emerald-200 hover:bg-emerald-700 transition-all" onclick="submitFinalWeight()">
                    查看成功总结
                </button>
            </div>
        </div>

        <!-- 9. 每日任务主页 -->
        <div id="dashboard" class="screen bg-emerald-50">
            <div class="p-6 bg-white rounded-b-3xl shadow-sm">
                <div class="flex justify-between items-center">
                    <button class="p-2 rounded-full hover:bg-gray-100"><i data-lucide="user-circle-2"></i></button>
                    <h1 id="dashboard-title" class="text-xl font-bold text-gray-800">轻断食 · 第一天</h1>
                    <button class="p-2 rounded-full hover:bg-gray-100"><i data-lucide="help-circle"></i></button>
                </div>
                <div id="current-task" class="mt-6 bg-emerald-600 text-white p-5 rounded-xl shadow-lg shadow-emerald-200 relative">
                    <p class="text-sm opacity-80">当前任务 (05:00 - 10:00)</p>
                    <h2 class="text-2xl font-bold mt-1">卯时·大肠经 辰时·胃经</h2>
                    <p class="text-sm mt-2 opacity-80">晨功 + 排肠</p>
                    <button class="w-full mt-4 bg-white text-emerald-700 font-bold py-3 rounded-lg hover:bg-emerald-50 transition-all" onclick="showTaskDetail()">
                        查看任务详情
                    </button>
                </div>
            </div>
            
            <div class="p-6 space-y-4">
                <h3 class="font-semibold text-gray-700">今日全天安排</h3>
                <!-- Timeline -->
                <div id="timeline-container" class="relative border-l-2 border-gray-200 ml-3">
                    <!-- Timeline items will be populated by JavaScript -->
                </div>
            </div>

            <!-- 底部今日打卡按钮 -->
            <div class="p-6 sticky bottom-0 bg-white border-t">
                <button class="w-full bg-emerald-600 text-white font-bold py-4 rounded-xl shadow-lg shadow-emerald-200 hover:bg-emerald-700 transition-all" onclick="toggleModal('checkin-modal')">
                    <i data-lucide="check-circle" class="inline-block -mt-1 mr-2 h-4 w-4"></i>今日打卡
                </button>
            </div>
        </div>

        <!-- 8. 成果总结页 -->
        <div id="results" class="screen p-6 bg-gradient-to-b from-emerald-50 to-amber-50 flex flex-col justify-center text-center">
            <div class="flex-grow flex flex-col items-center justify-center">
                <i data-lucide="party-popper" class="w-16 h-16 text-amber-500"></i>
                <h1 id="results-title" class="text-3xl font-bold text-gray-800 mt-4">恭喜您，完成3日轻断食！</h1>
                <p class="text-gray-600 mt-2">这是了不起的坚持，来看看您的变化！</p>
                <div class="mt-8 bg-white/70 backdrop-blur-sm p-6 rounded-2xl shadow-md w-full">
                    <h3 class="font-semibold text-gray-800">您的成果报告</h3>
                    <div class="mt-4 grid grid-cols-2 gap-4">
                        <div class="text-left">
                            <p class="text-sm text-gray-500">初始体重</p>
                            <p class="text-xl font-bold text-gray-800">65.5 <span class="text-base font-medium">kg</span></p>
                        </div>
                        <div class="text-left">
                            <p class="text-sm text-gray-500">当前体重</p>
                            <p id="final-weight" class="text-xl font-bold text-gray-800">63.0 <span class="text-base font-medium">kg</span></p>
                        </div>
                        <div class="bg-emerald-100 p-4 rounded-lg col-span-2 text-center">
                            <p class="text-sm text-emerald-800">总计减重</p>
                            <p id="weight-loss" class="text-3xl font-bold text-emerald-600">-2.5 <span class="text-base font-medium">kg</span></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="py-6 space-y-3">
                <button class="w-full bg-emerald-600 text-white font-bold py-4 rounded-xl shadow-lg shadow-emerald-200 hover:bg-emerald-700 transition-all" onclick="showScreen('recovery-phase')">
                    <i data-lucide="utensils" class="inline-block -mt-1 mr-2"></i>开始复食阶段
                </button>
                <button class="w-full bg-gray-200 text-gray-800 font-bold py-4 rounded-xl shadow-lg hover:bg-gray-300 transition-all">
                    <i data-lucide="share-2" class="inline-block -mt-1 mr-2"></i>生成分享海报
                </button>
            </div>
        </div>

        <!-- 复食阶段页面 -->
        <div id="recovery-phase" class="screen bg-orange-50">
            <!-- 顶部导航 -->
            <div class="bg-white border-b sticky top-0 z-10">
                <div class="flex items-center justify-between p-4">
                    <div class="flex items-center">
                        <button onclick="showScreen('results')" class="p-2 rounded-full hover:bg-gray-100 mr-3">
                            <i data-lucide="arrow-left" class="w-5 h-5"></i>
                        </button>
                        <h1 class="text-lg font-semibold text-gray-800">复食阶段</h1>
                    </div>
                    <div class="text-sm text-orange-600 font-medium">
                        第<span id="recovery-day">1</span>天 / 3天
                    </div>
                </div>
            </div>

            <!-- 复食说明 -->
            <div class="p-4 bg-orange-100 border-b">
                <div class="flex items-start">
                    <i data-lucide="info" class="w-5 h-5 text-orange-600 mt-0.5 mr-3 flex-shrink-0"></i>
                    <div class="text-sm text-orange-800">
                        <p class="font-medium mb-1">复食阶段注意事项</p>
                        <p>轻断食后需要逐步恢复正常饮食，请按照指导进行清淡少食，避免暴饮暴食。</p>
                    </div>
                </div>
            </div>

            <!-- 复食任务时间线 -->
            <div class="p-6">
                <div class="relative" id="recovery-timeline">
                    <!-- 复食任务将通过JavaScript填充 -->
                </div>
            </div>

            <!-- 底部导航 -->
            <div class="p-6 sticky bottom-0 bg-white border-t flex space-x-3">
                <button id="prev-recovery-day" class="flex-1 bg-gray-200 text-gray-800 font-bold py-3 rounded-xl" onclick="changeRecoveryDay(-1)">
                    <i data-lucide="chevron-left" class="inline-block -mt-1 mr-1"></i>上一天
                </button>
                <button id="next-recovery-day" class="flex-1 bg-orange-600 text-white font-bold py-3 rounded-xl" onclick="changeRecoveryDay(1)">
                    下一天<i data-lucide="chevron-right" class="inline-block -mt-1 ml-1"></i>
                </button>
            </div>
        </div>

        <!-- 复食结束页面 -->
        <div id="recovery-complete" class="screen p-6 flex flex-col justify-center items-center text-center">
            <div class="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i data-lucide="trophy" class="w-10 h-10 text-orange-600"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">恭喜您完成复食阶段！</h1>
            <p class="text-gray-600 mb-8 leading-relaxed">
                您已成功完成整个轻断食计划，包括断食期和复食期。<br>
                这是一个了不起的成就，您的身体已经得到了很好的调理和恢复。
            </p>

            <div class="bg-orange-50 p-6 rounded-xl mb-8 w-full max-w-md">
                <h3 class="font-semibold text-orange-800 mb-3">您的收获</h3>
                <div class="space-y-2 text-sm text-orange-700">
                    <div class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 mr-2"></i>
                        <span>身体得到深度清理和调理</span>
                    </div>
                    <div class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 mr-2"></i>
                        <span>养成了健康的饮食习惯</span>
                    </div>
                    <div class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 mr-2"></i>
                        <span>掌握了养生功法和技巧</span>
                    </div>
                    <div class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 mr-2"></i>
                        <span>提升了身心健康水平</span>
                    </div>
                </div>
            </div>

            <div class="space-y-3 w-full max-w-md">
                <button class="w-full bg-orange-600 text-white font-bold py-4 rounded-xl shadow-lg shadow-orange-200 hover:bg-orange-700 transition-all" onclick="showScreen('dashboard')">
                    <i data-lucide="home" class="inline-block -mt-1 mr-2"></i>返回首页
                </button>
                <button class="w-full bg-gray-200 text-gray-800 font-bold py-4 rounded-xl shadow-lg hover:bg-gray-300 transition-all">
                    <i data-lucide="share-2" class="inline-block -mt-1 mr-2"></i>分享我的成就
                </button>
            </div>
        </div>

        <!-- Task Detail Screen -->
        <div id="task-detail" class="screen">
            <div class="bg-white border-b">
                <div class="flex items-center p-4">
                    <button onclick="showScreen('dashboard')" class="p-2 rounded-full hover:bg-gray-100 mr-3">
                        <i data-lucide="arrow-left" class="w-5 h-5"></i>
                    </button>
                    <h1 id="task-detail-title" class="text-lg font-semibold text-gray-800">任务详情</h1>
                </div>
            </div>
            <div id="task-detail-content" class="p-6">
                <!-- Task detail content will be populated by JavaScript -->
            </div>
        </div>

        <!-- Modal for Daily Check-in -->
        <div id="checkin-modal" class="modal-overlay" onclick="toggleModal('checkin-modal')">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="flex justify-between items-center mb-4">
                    <h2 id="checkin-title" class="text-lg font-bold text-gray-800">第一天 · 任务打卡</h2>
                    <button class="p-2 rounded-full hover:bg-gray-100" onclick="toggleModal('checkin-modal')"><i data-lucide="x"></i></button>
                </div>
                <div id="checkin-tasks" class="space-y-3 max-h-[60vh] overflow-y-auto">
                    <!-- Checkin tasks will be populated by JavaScript -->
                </div>
                <button class="w-full mt-4 bg-emerald-600 text-white font-bold py-3 rounded-xl" onclick="submitCheckin()">
                    确认打卡
                </button>
            </div>
        </div>

        <!-- Modal for Shipping Info -->
        <div id="shipping-modal" class="modal-overlay" onclick="toggleModal('shipping-modal')">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-bold text-gray-800">填写收货信息</h2>
                    <button class="p-2 rounded-full hover:bg-gray-100" onclick="toggleModal('shipping-modal')">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">收货人</label>
                            <input type="text" id="receiver-name" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500" placeholder="请输入收货人姓名">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                            <input type="tel" id="receiver-phone" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500" placeholder="请输入手机号码">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">收货地址</label>
                        <textarea id="receiver-address" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500" placeholder="请输入详细收货地址"></textarea>
                    </div>
                </div>
                <button class="w-full mt-6 bg-emerald-600 text-white font-bold py-3 rounded-xl" onclick="confirmShippingInfo()">
                    确认信息，去付款
                </button>
            </div>
        </div>

        <!-- Modal for Product Received Confirmation -->
        <div id="product-received-modal" class="modal-overlay" onclick="toggleModal('product-received-modal')">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="text-center p-4">
                    <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="package" class="w-8 h-8 text-emerald-600"></i>
                    </div>
                    <h2 class="text-xl font-bold text-gray-800 mb-2">确认收货</h2>
                    <p class="text-gray-600 mb-6">请确认您是否已收到轻断食产品？</p>
                    <div class="space-y-3">
                        <button class="w-full bg-emerald-600 text-white font-bold py-3 rounded-xl" onclick="confirmProductReceived()">
                            是的，我已收到产品
                        </button>
                        <button class="w-full bg-gray-200 text-gray-800 font-bold py-3 rounded-xl" onclick="toggleModal('product-received-modal')">
                            还没收到，稍后确认
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal for Exercise Instructions -->
        <div id="exercise-modal" class="modal-overlay" onclick="toggleModal('exercise-modal')">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="flex justify-between items-center mb-4">
                    <h2 id="exercise-title" class="text-lg font-bold text-gray-800">功法说明</h2>
                    <button class="p-2 rounded-full hover:bg-gray-100" onclick="toggleModal('exercise-modal')">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
                <div id="exercise-content" class="space-y-4">
                    <!-- 功法内容将通过JavaScript填充 -->
                </div>
            </div>
        </div>

        <!-- Modal for Meal Suggestions -->
        <div id="meal-modal" class="modal-overlay" onclick="toggleModal('meal-modal')">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="flex justify-between items-center mb-4">
                    <h2 id="meal-title" class="text-lg font-bold text-gray-800">饮食建议</h2>
                    <button class="p-2 rounded-full hover:bg-gray-100" onclick="toggleModal('meal-modal')">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
                <div id="meal-content" class="space-y-4">
                    <!-- 饮食建议内容将通过JavaScript填充 -->
                </div>
            </div>
        </div>

    </div>


   <script>
        // 初始化 Lucide 图标
        lucide.createIcons();

        // 全局变量
        let selectedPackage = '3'; // 默认3日包
        let currentDay = 1;

        // 轻断食任务数据 - 根据CSV文件更新
        const fastingData = {
            '2': {
                title: '2日轻断食',
                days: [
                    {
                        day: 1,
                        title: '第一日',
                        tasks: [
                            {
                                time: '5:00-10:00',
                                period: '卯时•大肠经 辰时•胃经',
                                name: '①上传最新数据',
                                items: '入关打卡，入关指标检测上传',
                                checklist: ['✅ 上传当日体重、血糖、血压等数据'],
                                hasVideo: false,
                                needsCheckin: false // 不需要食用产品，不需要打卡
                            },
                            {
                                time: '5:00-10:00',
                                period: '卯时•大肠经 辰时•胃经',
                                name: '②晨功',
                                items: '丹道药茶1杯(热）或陈桂苓茶（高压140+以上者）1包',
                                checklist: ['✅ 喝茶', '✅ 晨功（三选其一）', '早读《清静经》三遍 —— 经文链接', '桩功 —— 视频链接', '空腹行脚功 —— 视频链接'],
                                hasVideo: true,
                                videoTitle: '晨功练习',
                                needsCheckin: true // 需要食用产品，需要打卡
                            },
                            {
                                time: '5:00-10:00',
                                period: '辰时•胃经',
                                name: '③排肠',
                                items: '君仓清50-60g',
                                checklist: ['✅ 排肠 —— 视频《健康从"肠"计议》'],
                                hasVideo: true,
                                videoTitle: '健康从"肠"计议',
                                needsCheckin: true // 需要食用产品，需要打卡
                            },
                            {
                                time: '11:00-13:00',
                                period: '午时·心经',
                                name: '④午斋、午休',
                                items: '服气吞津9/18/27口',
                                checklist: ['✅ 服气吞津—— 视频链接'],
                                hasVideo: true,
                                videoTitle: '服气吞津功法',
                                needsCheckin: false // 功法练习，不需要打卡
                            },
                            {
                                time: '15:00-17:00',
                                period: '申时 • 膀胱经',
                                name: '⑤扶阳下午茶',
                                items: '非高血压：丹道药茶1杯(热）+回春丹2颗；高压140+以上者：陈桂苓茶1包+滋阴丸2颗',
                                checklist: ['✅ 喝茶', '✅ 运动升阳（三选其一）', '行脚/拉筋/拍打肝胆经'],
                                hasVideo: true,
                                videoTitle: '运动升阳功法',
                                needsCheckin: true // 需要食用产品，需要打卡
                            },
                            {
                                time: '17:00-19:00',
                                period: '酉时•肾经',
                                name: '⑥晚断食',
                                items: '服气吞津9/18/27口',
                                checklist: ['✅ 服气吞津—— 视频链接'],
                                hasVideo: true,
                                videoTitle: '服气吞津功法',
                                needsCheckin: false // 功法练习，不需要打卡
                            },
                            {
                                time: '21:00-23:00',
                                period: '亥时• 三焦经',
                                name: '⑦肝胆排毒',
                                items: '肝胆排毒液300-360ml',
                                checklist: ['✅ 肝胆排毒'],
                                hasVideo: false,
                                needsCheckin: true // 需要食用产品，需要打卡
                            },
                            {
                                time: '21:00-23:00',
                                period: '亥时• 三焦经',
                                name: '⑧晚课',
                                items: '',
                                checklist: ['✅ 止静功法——视频链接'],
                                hasVideo: true,
                                videoTitle: '止静功法',
                                needsCheckin: false // 功法练习，不需要打卡
                            }
                        ]
                    },
                    {
                        day: 2,
                        title: '第二日',
                        tasks: [
                            {
                                time: '5:00-10:00',
                                period: '卯时•大肠经',
                                name: '①晨功',
                                items: '丹道药茶1杯(热）或陈桂苓茶（高压140+以上者）1包',
                                checklist: ['✅ 喝茶', '✅ 晨功（三选其一）', '早读《清静经》三遍 —— 经文链接', '日用养生功法 —— 视频链接', '空腹行脚功 —— 视频链接'],
                                hasVideo: true,
                                videoTitle: '晨功练习'
                            },
                            {
                                time: '5:00-10:00',
                                period: '辰时•胃经',
                                name: '②排肠',
                                items: '君仓清50-60g',
                                checklist: ['✅ 排肠 —— 视频《健康从"肠"计议》'],
                                hasVideo: true,
                                videoTitle: '健康从"肠"计议'
                            },
                            {
                                time: '11:00-13:00',
                                period: '午时·心经',
                                name: '③午斋课',
                                items: '1、服气吞津9/18/27口；2、7点前完成排肠的可恢复清淡饮食',
                                checklist: ['✅ 服气吞津—— 视频链接'],
                                hasVideo: true,
                                videoTitle: '服气吞津功法'
                            },
                            {
                                time: '15:00-17:00',
                                period: '申时 • 膀胱经',
                                name: '④扶阳下午茶',
                                items: '非高血压：丹道药茶1杯(热）+回春丹2颗；高压140+以上者：陈桂苓茶1包+滋阴丸2颗',
                                checklist: ['✅ 喝茶', '✅ 运动升阳（三选其一）', '行脚/拉筋/拍打肝胆经'],
                                hasVideo: true,
                                videoTitle: '运动升阳功法'
                            },
                            {
                                time: '17:00-19:00',
                                period: '酉时•肾经',
                                name: '⑤晚斋功',
                                items: '清淡饮食/食谱/回春丹或滋阴丸1-2颗；服气吞津9/18/27口',
                                checklist: ['✅ 滋阴丸/回春丹代餐', '✅ 服气吞津—— 视频链接'],
                                hasVideo: true,
                                videoTitle: '服气吞津功法'
                            },
                            {
                                time: '21:00-23:00',
                                period: '亥时• 三焦经',
                                name: '⑥晚课',
                                items: '',
                                checklist: ['✅ 止静功法——视频链接'],
                                hasVideo: true,
                                videoTitle: '止静功法'
                            }
                        ]
                    },
                    {
                        day: 3,
                        title: '第三日（恢复）',
                        tasks: [
                            {
                                time: '5:00-11:00',
                                period: '卯时•大肠经 已时 • 脾经',
                                name: '①晨功+早餐',
                                items: '清淡饮食或继续代餐断食；非高血压：丹道药茶1杯(热）+回春丹2颗；高压140+以上者：陈桂苓茶1包+滋阴丸2颗',
                                checklist: ['✅ 喝茶', '✅ 晨功（三选其一）', '早读《清静经》三遍 —— 经文链接', '桩功 —— 视频链接', '空腹行脚功 —— 视频链接'],
                                hasVideo: true,
                                videoTitle: '晨功练习'
                            },
                            {
                                time: '11:00-13:00',
                                period: '午时·心经',
                                name: '②内观午餐',
                                items: '清淡饮食',
                                checklist: ['✅ 内观午餐 —— 视频链接'],
                                hasVideo: true,
                                videoTitle: '内观午餐指导'
                            },
                            {
                                time: '15:00-17:00',
                                period: '申时 • 膀胱经',
                                name: '③扶阳下午茶',
                                items: '非高血压：丹道药茶1杯(热）+回春丹2颗；高压140+以上者：陈桂苓茶1包+滋阴丸2颗',
                                checklist: ['✅ 喝茶', '✅ 运动升阳（三选其一）', '行脚/拉筋/拍打肝胆经'],
                                hasVideo: true,
                                videoTitle: '运动升阳功法'
                            },
                            {
                                time: '17:00-19:00',
                                period: '酉时•肾经',
                                name: '④内观晚餐',
                                items: '清淡饮食或继续回春丹/滋阴丸代餐',
                                checklist: ['✅ 内观晚餐 —— 视频链接'],
                                hasVideo: true,
                                videoTitle: '内观晚餐指导'
                            },
                            {
                                time: '21:00-23:00',
                                period: '亥时• 三焦经',
                                name: '⑤晚课',
                                items: '',
                                checklist: ['✅ 止静功法——视频链接'],
                                hasVideo: true,
                                videoTitle: '止静功法'
                            }
                        ]
                    }
                ]
            },
            '3': {
                title: '3日轻断食',
                days: [
                    {
                        day: 1,
                        title: '第一日',
                        tasks: [
                            {
                                time: '5:00-10:00',
                                period: '卯时•大肠经 辰时•胃经',
                                name: '①上传最新数据',
                                items: '入关打卡，入关指标检测上传',
                                checklist: ['✅ 上传当日体重、血糖、血压等数据'],
                                hasVideo: false
                            },
                            {
                                time: '5:00-10:00',
                                period: '卯时•大肠经 辰时•胃经',
                                name: '②晨功',
                                items: '丹道药茶1杯(热）或陈桂苓茶（高压140+以上者）1包',
                                checklist: ['✅ 喝茶', '✅ 晨功（三选其一）', '早读《清静经》三遍 —— 经文链接', '桩功 —— 视频链接', '空腹行脚功 —— 视频链接'],
                                hasVideo: true,
                                videoTitle: '晨功练习'
                            },
                            {
                                time: '5:00-10:00',
                                period: '辰时•胃经',
                                name: '③排肠',
                                items: '君仓清50-60g',
                                checklist: ['✅ 排肠 —— 视频《健康从"肠"计议》'],
                                hasVideo: true,
                                videoTitle: '健康从"肠"计议'
                            },
                            {
                                time: '11:00-13:00',
                                period: '午时·心经',
                                name: '④午斋、午休',
                                items: '服气吞津9/18/27口，饥饿感强烈/低血糖者食用回春丹1颗',
                                checklist: ['✅ 服气吞津—— 视频链接'],
                                hasVideo: true,
                                videoTitle: '服气吞津功法'
                            },
                            {
                                time: '15:00-17:00',
                                period: '申时 • 膀胱经',
                                name: '⑤扶阳下午茶',
                                items: '非高血压：丹道药茶1杯(热）+回春丹1-2颗；高压140+以上者：陈桂苓茶1包+滋阴丸2颗',
                                checklist: ['✅ 喝茶', '✅ 运动升阳（三选其一）', '行脚/拉筋/拍打肝胆经'],
                                hasVideo: true,
                                videoTitle: '运动升阳功法'
                            },
                            {
                                time: '17:00-19:00',
                                period: '酉时•肾经',
                                name: '⑥晚断食',
                                items: '服气吞津9/18/27口',
                                checklist: ['✅ 服气吞津—— 视频链接'],
                                hasVideo: true,
                                videoTitle: '服气吞津功法'
                            },
                            {
                                time: '21:00-23:00',
                                period: '亥时• 三焦经',
                                name: '⑦肝胆排毒',
                                items: '肝胆排毒液180ml（6瓶）',
                                checklist: ['✅ 肝胆排毒'],
                                hasVideo: false
                            },
                            {
                                time: '21:00-23:00',
                                period: '亥时• 三焦经',
                                name: '⑧晚课',
                                items: '',
                                checklist: ['✅ 止静功法——视频链接'],
                                hasVideo: true,
                                videoTitle: '止静功法'
                            }
                        ]
                    },
                    {
                        day: 2,
                        title: '第二日',
                        tasks: [
                            {
                                time: '5:00-10:00',
                                period: '卯时•大肠经 辰时•胃经',
                                name: '②晨功',
                                items: '丹道药茶1杯(热）或陈桂苓茶（高压140+以上者）1包',
                                checklist: ['✅ 喝茶', '✅ 晨功（三选其一）', '早读《清静经》三遍 —— 经文链接', '桩功 —— 视频链接', '空腹行脚功 —— 视频链接'],
                                hasVideo: true,
                                videoTitle: '晨功练习'
                            },
                            {
                                time: '5:00-10:00',
                                period: '辰时•胃经',
                                name: '③排肠',
                                items: '君仓清50-60g',
                                checklist: ['✅ 排肠 —— 视频《健康从"肠"计议》'],
                                hasVideo: true,
                                videoTitle: '健康从"肠"计议'
                            },
                            {
                                time: '11:00-13:00',
                                period: '午时·心经',
                                name: '④午斋、午休',
                                items: '服气吞津9/18/27口，饥饿感强烈/低血糖者食用回春丹1颗',
                                checklist: ['✅ 服气吞津—— 视频链接'],
                                hasVideo: true,
                                videoTitle: '服气吞津功法'
                            },
                            {
                                time: '15:00-17:00',
                                period: '申时 • 膀胱经',
                                name: '⑤扶阳下午茶',
                                items: '非高血压：丹道药茶1杯(热）+回春丹1-2颗；高压140+以上者：陈桂苓茶1包+滋阴丸2颗',
                                checklist: ['✅ 喝茶', '✅ 运动升阳（三选其一）', '行脚/拉筋/拍打肝胆经'],
                                hasVideo: true,
                                videoTitle: '运动升阳功法'
                            },
                            {
                                time: '17:00-19:00',
                                period: '酉时•肾经',
                                name: '⑥晚断食',
                                items: '服气吞津9/18/27口',
                                checklist: ['✅ 服气吞津—— 视频链接'],
                                hasVideo: true,
                                videoTitle: '服气吞津功法'
                            },
                            {
                                time: '21:00-23:00',
                                period: '亥时• 三焦经',
                                name: '⑦肝胆排毒',
                                items: '肝胆排毒液180ml（6瓶）',
                                checklist: ['✅ 肝胆排毒'],
                                hasVideo: false
                            },
                            {
                                time: '21:00-23:00',
                                period: '亥时• 三焦经',
                                name: '⑧晚课',
                                items: '',
                                checklist: ['✅ 止静功法——视频链接'],
                                hasVideo: true,
                                videoTitle: '止静功法'
                            }
                        ]
                    },
                    {
                        day: 3,
                        title: '第三日',
                        tasks: [
                            {
                                time: '5:00-10:00',
                                period: '卯时•大肠经',
                                name: '①晨功',
                                items: '丹道药茶1杯(热）或陈桂苓茶（高压140+以上者）1包',
                                checklist: ['✅ 喝茶', '✅ 晨功（三选其一）', '早读《清静经》三遍 —— 经文链接', '日用养生功法 —— 视频链接', '空腹行脚功 —— 视频链接'],
                                hasVideo: true,
                                videoTitle: '晨功练习'
                            },
                            {
                                time: '5:00-10:00',
                                period: '辰时•胃经',
                                name: '②排肠',
                                items: '君仓清50-60g',
                                checklist: ['✅ 排肠 —— 视频《健康从"肠"计议》'],
                                hasVideo: true,
                                videoTitle: '健康从"肠"计议'
                            },
                            {
                                time: '11:00-13:00',
                                period: '午时·心经',
                                name: '③午斋课',
                                items: '1、服气吞津9/18/27口；2、7点前完成排肠的可恢复清淡饮食',
                                checklist: ['✅ 服气吞津—— 视频链接'],
                                hasVideo: true,
                                videoTitle: '服气吞津功法'
                            },
                            {
                                time: '15:00-17:00',
                                period: '申时 • 膀胱经',
                                name: '④扶阳下午茶',
                                items: '非高血压：丹道药茶1杯(热）+回春丹1颗；高压140+以上者：陈桂苓茶1包+滋阴丸2颗',
                                checklist: ['✅ 喝茶', '✅ 运动升阳（三选其一）', '行脚/拉筋/拍打肝胆经'],
                                hasVideo: true,
                                videoTitle: '运动升阳功法'
                            },
                            {
                                time: '17:00-19:00',
                                period: '酉时•肾经',
                                name: '⑤晚斋功',
                                items: '清淡饮食/食谱/回春丹或滋阴丸1-2颗；服气吞津9/18/27口',
                                checklist: ['✅ 滋阴丸/回春丹代餐', '✅ 服气吞津—— 视频链接'],
                                hasVideo: true,
                                videoTitle: '服气吞津功法'
                            },
                            {
                                time: '21:00-23:00',
                                period: '亥时• 三焦经',
                                name: '⑥晚课',
                                items: '',
                                checklist: ['✅ 止静功法——视频链接'],
                                hasVideo: true,
                                videoTitle: '止静功法'
                            }
                        ]
                    }
                ]
            }
        };

        // 当前选中的天数
        let currentTaskIndex = 0;

        // 页面切换逻辑
        const screens = document.querySelectorAll('.screen');
        const navButtons = document.querySelectorAll('.nav-button');

        function showScreen(screenId) {
            screens.forEach(screen => screen.classList.remove('active'));
            const activeScreen = document.getElementById(screenId);
            if (activeScreen) {
                activeScreen.classList.add('active');
                activeScreen.scrollTop = 0;
            }

            navButtons.forEach(button => {
                button.classList.remove('active');
                if (button.getAttribute('onclick').includes(screenId)) {
                    button.classList.add('active');
                }
            });

            // 显示或隐藏每日导航
            const dailyNav = document.getElementById('daily-nav');
            if (screenId === 'dashboard') {
                dailyNav.style.display = 'flex';
                updateDailyNav();
                updateDashboard();
            } else {
                dailyNav.style.display = 'none';
            }

            // 如果显示results，更新成果页面
            if (screenId === 'results') {
                updateResults();
            }
        }

        // 更新每日导航按钮
        function updateDailyNav() {
            const data = fastingData[selectedPackage];
            const dailyButtons = document.getElementById('daily-buttons');
            let buttonsHTML = '';

            data.days.forEach((day, index) => {
                const isActive = index === currentDay - 1;
                buttonsHTML += `
                    <button class="px-4 py-2 text-sm rounded-full shadow-sm transition-all ${isActive ? 'bg-emerald-600 text-white' : 'bg-white text-gray-700 hover:bg-emerald-50'}"
                            onclick="switchDay(${day.day})">
                        ${day.title}
                    </button>
                `;
            });

            dailyButtons.innerHTML = buttonsHTML;
        }

        // 切换天数
        function switchDay(day) {
            currentDay = day;
            updateDailyNav();
            updateDashboard();
        }

        // 合并同一时间段的任务
        function mergeTasksByTime(tasks) {
            const timeGroups = {};

            tasks.forEach((task, index) => {
                if (!timeGroups[task.time]) {
                    timeGroups[task.time] = {
                        time: task.time,
                        period: task.period,
                        tasks: [],
                        originalIndexes: []
                    };
                }
                timeGroups[task.time].tasks.push(task);
                timeGroups[task.time].originalIndexes.push(index);
            });

            return Object.values(timeGroups);
        }

        // 更新每日任务页面
        function updateDashboard() {
            const data = fastingData[selectedPackage];
            const dayData = data.days[currentDay - 1];

            // 更新标题
            document.getElementById('dashboard-title').textContent = `轻断食 · ${dayData.title}`;

            // 合并同一时间段的任务
            const mergedTasks = mergeTasksByTime(dayData.tasks);

            // 更新当前任务
            const currentMergedTask = mergedTasks.find(group =>
                group.originalIndexes.includes(currentTaskIndex)
            ) || mergedTasks[0];

            const currentTaskDiv = document.getElementById('current-task');
            const taskNames = currentMergedTask.tasks.map(t => t.name).join(' ');
            currentTaskDiv.innerHTML = `
                <p class="text-sm opacity-80">当前任务 (${currentMergedTask.time})</p>
                <h2 class="text-2xl font-bold mt-1">${currentMergedTask.period}</h2>
                <p class="text-sm mt-2 opacity-80">${taskNames}</p>
                <button class="w-full mt-4 bg-white text-emerald-700 font-bold py-3 rounded-lg hover:bg-emerald-50 transition-all" onclick="showMergedTaskDetail('${currentMergedTask.time}')">
                    查看任务详情
                </button>
            `;

            // 更新时间线
            const timelineContainer = document.getElementById('timeline-container');
            let timelineHTML = '';

            mergedTasks.forEach((group, groupIndex) => {
                const isCompleted = group.originalIndexes.every(idx => idx < currentTaskIndex);
                const isCurrent = group.originalIndexes.includes(currentTaskIndex);
                const taskNames = group.tasks.map(t => t.name).join(' ');

                timelineHTML += `
                    <div class="timeline-item mb-6 ml-8 cursor-pointer" onclick="showMergedTaskDetail('${group.time}')">
                        <span class="absolute flex items-center justify-center w-6 h-6 ${isCompleted ? 'bg-green-200' : isCurrent ? 'bg-emerald-200' : 'bg-gray-200'} rounded-full -left-3 ring-4 ring-white">
                            <i data-lucide="${isCompleted ? 'check' : isCurrent ? 'clock' : 'circle'}" class="w-4 h-4 ${isCompleted ? 'text-green-700' : isCurrent ? 'text-emerald-700' : 'text-gray-500'}"></i>
                        </span>
                        <div class="content p-4 rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow ${isCurrent ? 'border border-emerald-300' : ''}">
                            <p class="text-sm text-gray-500">${group.time}</p>
                            <p class="font-semibold ${isCurrent ? 'text-emerald-800' : 'text-gray-800'}">${group.period}</p>
                            <p class="text-xs text-gray-400 mt-1">${taskNames}</p>
                        </div>
                    </div>
                `;
            });

            timelineContainer.innerHTML = timelineHTML;

            // 更新打卡模态框
            updateCheckinModal();

            // 重新初始化图标
            lucide.createIcons();
        }

        // 更新打卡模态框
        function updateCheckinModal() {
            const data = fastingData[selectedPackage];
            const dayData = data.days[currentDay - 1];

            document.getElementById('checkin-title').textContent = `${dayData.title} · 产品打卡`;

            const checkinTasks = document.getElementById('checkin-tasks');
            let tasksHTML = '';

            // 只显示排肠和肝胆排毒任务
            const checkinTasksData = dayData.tasks.filter(task =>
                task.name.includes('排肠') || task.name.includes('肝胆排毒')
            );

            if (checkinTasksData.length === 0) {
                tasksHTML = '<p class="text-gray-500 text-center py-4">今日无需产品打卡</p>';
            } else {
                checkinTasksData.forEach((task, index) => {
                    const isChecked = index < currentTaskIndex; // 根据当前任务进度判断是否已完成
                    tasksHTML += `
                        <label class="flex items-start p-4 bg-gray-50 rounded-lg border hover:bg-gray-100 transition-colors">
                            <input type="checkbox" ${isChecked ? 'checked' : ''} class="h-5 w-5 rounded text-emerald-600 focus:ring-emerald-500 mt-0.5 mr-3">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">${task.name}</div>
                                <div class="text-sm text-gray-500 mt-1">${task.time}</div>
                                <div class="text-sm text-blue-600 mt-1">${task.items}</div>
                            </div>
                        </label>
                    `;
                });
            }

            checkinTasks.innerHTML = tasksHTML;
        }

        // 监听产品包选择
        document.addEventListener('change', function(e) {
            if (e.target.name === 'package') {
                selectedPackage = e.target.value;
            }
        });

        // Modal 切换逻辑
        function toggleModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.toggle('active');
            }
        }



        // 更新付款页面信息
        function updatePaymentInfo() {
            const shippingInfo = JSON.parse(localStorage.getItem('shippingInfo') || '{}');

            // 更新订单摘要
            const orderSummary = document.getElementById('order-summary');
            orderSummary.innerHTML = `
                <div class="flex justify-between">
                    <span>${selectedPackageType}日轻断食产品包</span>
                    <span>¥ ${selectedPackageType === 2 ? '688' : '888'}</span>
                </div>
            `;

            // 更新收货信息
            const shippingSummary = document.getElementById('shipping-summary');
            if (shippingSummary) {
                shippingSummary.innerHTML = `
                    <p><strong>收货人：</strong>${shippingInfo.name}</p>
                    <p><strong>联系电话：</strong>${shippingInfo.phone}</p>
                    <p><strong>收货地址：</strong>${shippingInfo.address}</p>
                `;
            }

            // 更新总价
            const paymentTotal = document.getElementById('payment-total');
            if (paymentTotal) {
                paymentTotal.textContent = `¥ ${selectedPackageType === 2 ? '688' : '888'}`;
            }
        }

        // 处理付款
        function processPayment() {
            // 显示loading页面
            showScreen('payment-loading');

            // 模拟付款处理延迟
            setTimeout(() => {
                const shippingInfo = JSON.parse(localStorage.getItem('shippingInfo') || '{}');

                // 更新支付完成页面的收货信息
                document.getElementById('delivery-address').textContent = shippingInfo.address;
                document.getElementById('delivery-contact').textContent = `${shippingInfo.name} ${shippingInfo.phone}`;

                showScreen('payment-success');
            }, 2000); // 2秒后显示支付完成页面
        }

        // 检查产品是否收到
        function checkProductReceived() {
            toggleModal('product-received-modal');
        }

        // 确认产品已收到
        function confirmProductReceived() {
            toggleModal('product-received-modal');
            showScreen('weight-info');
        }

        // 开始轻断食之旅
        function startFastingJourney() {
            const weight = document.getElementById('initial-weight').value;

            if (!weight) {
                alert('请输入您的体重');
                return;
            }

            // 保存初始体重
            localStorage.setItem('initialWeight', weight);

            showScreen('dashboard');
        }

        // 确认收货信息
        function confirmShippingInfo() {
            const receiverName = document.getElementById('receiver-name').value;
            const receiverPhone = document.getElementById('receiver-phone').value;
            const receiverAddress = document.getElementById('receiver-address').value;

            if (!receiverName || !receiverPhone || !receiverAddress) {
                alert('请填写完整的收货信息');
                return;
            }

            // 保存收货信息
            localStorage.setItem('shippingInfo', JSON.stringify({
                name: receiverName,
                phone: receiverPhone,
                address: receiverAddress
            }));

            toggleModal('shipping-modal');
            updatePaymentInfo();
            showScreen('payment');
        }

        // 复食阶段数据
        const recoveryData = {
            days: [
                {
                    day: 1,
                    title: '复食第一天',
                    tasks: [
                        {
                            time: '5:00-11:00',
                            name: '晨功',
                            items: '丹道药茶1杯（高血压用陈桂苓茶代替）',
                            checklist: [
                                { text: '喝茶', type: 'check' },
                                { text: '晨功（三选其一）', type: 'check' },
                                { text: '早读《清静经》三遍', type: 'link', action: 'showExerciseInfo', data: 'qingjing' },
                                { text: '日用养生功法', type: 'link', action: 'showExerciseInfo', data: 'yangsheng' },
                                { text: '空腹行脚功', type: 'link', action: 'showExerciseInfo', data: 'xingjiao' }
                            ],
                            needsCheckin: true
                        },
                        {
                            time: '7:00',
                            name: '早餐',
                            items: '清淡少食健康食谱+回春丹/滋阴丸1颗',
                            checklist: [
                                { text: '早餐', type: 'check' }
                            ],
                            suggestion: { text: '早餐建议（搭配/代餐）', action: 'showMealSuggestion', data: 'breakfast' },
                            needsCheckin: true
                        },
                        {
                            time: '11:00',
                            name: '中餐',
                            items: '清淡少食健康食谱+回春丹/滋阴丸1颗',
                            checklist: [
                                { text: '午餐', type: 'check' }
                            ],
                            suggestion: { text: '午餐建议（搭配/代餐）', action: 'showMealSuggestion', data: 'lunch' },
                            needsCheckin: true
                        },
                        {
                            time: '17:00',
                            name: '晚餐',
                            items: '清淡少食健康食谱+回春丹/滋阴丸1颗',
                            checklist: [
                                { text: '晚餐', type: 'check' }
                            ],
                            suggestion: { text: '晚餐建议（搭配/代餐）', action: 'showMealSuggestion', data: 'dinner' },
                            needsCheckin: true
                        }
                    ]
                },
                // 第2天和第3天的数据结构相同
                {
                    day: 2,
                    title: '复食第二天',
                    tasks: [
                        {
                            time: '5:00-11:00',
                            name: '晨功',
                            items: '丹道药茶1杯（高血压用陈桂苓茶代替）',
                            checklist: [
                                { text: '喝茶', type: 'check' },
                                { text: '晨功（三选其一）', type: 'check' },
                                { text: '早读《清静经》三遍', type: 'link', action: 'showExerciseInfo', data: 'qingjing' },
                                { text: '日用养生功法', type: 'link', action: 'showExerciseInfo', data: 'yangsheng' },
                                { text: '空腹行脚功', type: 'link', action: 'showExerciseInfo', data: 'xingjiao' }
                            ],
                            needsCheckin: true
                        },
                        {
                            time: '7:00',
                            name: '早餐',
                            items: '清淡少食健康食谱+回春丹/滋阴丸1颗',
                            checklist: [
                                { text: '早餐', type: 'check' }
                            ],
                            suggestion: { text: '早餐建议（搭配/代餐）', action: 'showMealSuggestion', data: 'breakfast' },
                            needsCheckin: true
                        },
                        {
                            time: '11:00',
                            name: '中餐',
                            items: '清淡少食健康食谱+回春丹/滋阴丸1颗',
                            checklist: [
                                { text: '午餐', type: 'check' }
                            ],
                            suggestion: { text: '午餐建议（搭配/代餐）', action: 'showMealSuggestion', data: 'lunch' },
                            needsCheckin: true
                        },
                        {
                            time: '17:00',
                            name: '晚餐',
                            items: '清淡少食健康食谱+回春丹/滋阴丸1颗',
                            checklist: [
                                { text: '晚餐', type: 'check' }
                            ],
                            suggestion: { text: '晚餐建议（搭配/代餐）', action: 'showMealSuggestion', data: 'dinner' },
                            needsCheckin: true
                        }
                    ]
                },
                {
                    day: 3,
                    title: '复食第三天',
                    tasks: [
                        {
                            time: '5:00-11:00',
                            name: '晨功',
                            items: '丹道药茶1杯（高血压用陈桂苓茶代替）',
                            checklist: [
                                { text: '喝茶', type: 'check' },
                                { text: '晨功（三选其一）', type: 'check' },
                                { text: '早读《清静经》三遍', type: 'link', action: 'showExerciseInfo', data: 'qingjing' },
                                { text: '日用养生功法', type: 'link', action: 'showExerciseInfo', data: 'yangsheng' },
                                { text: '空腹行脚功', type: 'link', action: 'showExerciseInfo', data: 'xingjiao' }
                            ],
                            needsCheckin: true
                        },
                        {
                            time: '7:00',
                            name: '早餐',
                            items: '清淡少食健康食谱+回春丹/滋阴丸1颗',
                            checklist: [
                                { text: '早餐', type: 'check' }
                            ],
                            suggestion: { text: '早餐建议（搭配/代餐）', action: 'showMealSuggestion', data: 'breakfast' },
                            needsCheckin: true
                        },
                        {
                            time: '11:00',
                            name: '中餐',
                            items: '清淡少食健康食谱+回春丹/滋阴丸1颗',
                            checklist: [
                                { text: '午餐', type: 'check' }
                            ],
                            suggestion: { text: '午餐建议（搭配/代餐）', action: 'showMealSuggestion', data: 'lunch' },
                            needsCheckin: true
                        },
                        {
                            time: '17:00',
                            name: '晚餐',
                            items: '清淡少食健康食谱+回春丹/滋阴丸1颗',
                            checklist: [
                                { text: '晚餐', type: 'check' }
                            ],
                            suggestion: { text: '晚餐建议（搭配/代餐）', action: 'showMealSuggestion', data: 'dinner' },
                            needsCheckin: true
                        }
                    ]
                }
            ]
        };

        let currentRecoveryDay = 1;
        let selectedPackageType = 3; // 默认选中3日产品包

        // 开始轻断食之旅
        function startFastingJourney() {
            const startDate = document.getElementById('start-date').value;
            const initialWeight = document.getElementById('initial-weight').value;

            if (!startDate) {
                alert('请选择开始日期');
                return;
            }

            if (!initialWeight) {
                alert('请填写您的初始体重');
                return;
            }

            // 检查是否到了开始日期
            const today = new Date();
            const selectedDate = new Date(startDate);

            // 重置时间为当天开始，便于比较
            today.setHours(0, 0, 0, 0);
            selectedDate.setHours(0, 0, 0, 0);

            if (selectedDate > today) {
                // 还没到开始日期，显示倒计时
                updateCountdown();
                return;
            }

            // 保存开始信息
            localStorage.setItem('fastingStartDate', startDate);
            localStorage.setItem('initialWeight', initialWeight);
            localStorage.setItem('selectedPackage', selectedPackageType);

            // 进入每日任务页面
            showScreen('dashboard');
        }

        // 提交最终体重
        function submitFinalWeight() {
            const finalWeight = document.getElementById('final-weight').value;
            if (!finalWeight) {
                alert('请填写您的最终体重');
                return;
            }

            // 保存最终体重
            localStorage.setItem('finalWeight', finalWeight);

            // 进入成功总结页面
            showScreen('results');
        }

        // 计算并显示日期范围
        function updateDateRange() {
            const startDateInput = document.getElementById('start-date');
            const dateRangeDisplay = document.getElementById('date-range-display');
            const planType = document.getElementById('plan-type');
            const displayStartDate = document.getElementById('display-start-date');
            const displayEndDate = document.getElementById('display-end-date');

            if (startDateInput.value) {
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(startDate);
                endDate.setDate(startDate.getDate() + selectedPackageType - 1);

                // 格式化日期显示
                const formatDate = (date) => {
                    return date.toLocaleDateString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                    });
                };

                planType.textContent = `${selectedPackageType}日轻断食`;
                displayStartDate.textContent = formatDate(startDate);
                displayEndDate.textContent = formatDate(endDate);

                dateRangeDisplay.style.display = 'block';

                // 启动倒计时
                startCountdownTimer();
            } else {
                dateRangeDisplay.style.display = 'none';

                // 清除倒计时
                if (countdownInterval) {
                    clearInterval(countdownInterval);
                }
            }
        }

        // 添加企业微信
        function addWechat() {
            alert('请扫描二维码或搜索企业微信号：JunRenHealth\n我们的专业团队将为您提供个性化指导和答疑服务。');
        }

        // 更新倒计时
        function updateCountdown() {
            const startDate = document.getElementById('start-date').value;
            const submitBtn = document.querySelector('#weight-info button');

            if (!startDate) return;

            const now = new Date();
            const targetDate = new Date(startDate);
            targetDate.setHours(0, 0, 0, 0);

            const timeDiff = targetDate.getTime() - now.getTime();

            if (timeDiff <= 0) {
                // 已经到了开始日期
                submitBtn.textContent = '确认轻断食计划';
                submitBtn.disabled = false;
                submitBtn.classList.remove('bg-gray-400');
                submitBtn.classList.add('bg-emerald-600', 'hover:bg-emerald-700');
                return;
            }

            // 计算剩余时间
            const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

            let countdownText = '还剩 ';
            if (days > 0) {
                countdownText += `${days}天 `;
            }
            countdownText += `${hours}小时 ${minutes}分`;

            submitBtn.textContent = countdownText;
            submitBtn.disabled = true;
            submitBtn.classList.remove('bg-emerald-600', 'hover:bg-emerald-700');
            submitBtn.classList.add('bg-gray-400');
        }

        // 开始倒计时定时器
        let countdownInterval;
        function startCountdownTimer() {
            // 清除之前的定时器
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }

            // 立即更新一次
            updateCountdown();

            // 每分钟更新一次倒计时
            countdownInterval = setInterval(updateCountdown, 60000);
        }

        // 切换产品包
        function switchPackage(packageType) {
            selectedPackageType = packageType;

            // 更新标签样式
            const tab2 = document.getElementById('package-tab-2');
            const tab3 = document.getElementById('package-tab-3');
            const content2 = document.getElementById('package-2-content');
            const content3 = document.getElementById('package-3-content');
            const radio2 = document.getElementById('package-2-radio');
            const radio3 = document.getElementById('package-3-radio');

            if (packageType === 2) {
                // 选中2日产品包
                tab2.classList.add('bg-emerald-600', 'text-white');
                tab2.classList.remove('text-gray-600');
                tab3.classList.remove('bg-emerald-600', 'text-white');
                tab3.classList.add('text-gray-600');

                content2.classList.remove('hidden');
                content3.classList.add('hidden');

                radio2.checked = true;
                radio3.checked = false;
            } else {
                // 选中3日产品包
                tab3.classList.add('bg-emerald-600', 'text-white');
                tab3.classList.remove('text-gray-600');
                tab2.classList.remove('bg-emerald-600', 'text-white');
                tab2.classList.add('text-gray-600');

                content3.classList.remove('hidden');
                content2.classList.add('hidden');

                radio3.checked = true;
                radio2.checked = false;
            }

            // 更新日期范围显示
            updateDateRange();

            // 更新倒计时
            setTimeout(startCountdownTimer, 100);
        }

        // 更新复食阶段页面
        function updateRecoveryPhase() {
            const dayData = recoveryData.days[currentRecoveryDay - 1];

            // 更新天数显示
            document.getElementById('recovery-day').textContent = currentRecoveryDay;

            // 更新时间线
            const timeline = document.getElementById('recovery-timeline');
            let timelineHTML = '';

            dayData.tasks.forEach((task, index) => {
                const isLast = index === dayData.tasks.length - 1;

                timelineHTML += `
                    <div class="flex items-start mb-6">
                        <div class="flex flex-col items-center mr-4">
                            <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                            ${!isLast ? '<div class="w-0.5 h-16 bg-orange-200 mt-2"></div>' : ''}
                        </div>
                        <div class="flex-1 bg-white p-4 rounded-lg shadow-sm">
                            <div class="flex justify-between items-start mb-2">
                                <div>
                                    <h3 class="font-semibold text-gray-800">${task.name}</h3>
                                    <p class="text-sm text-orange-600">${task.time}</p>
                                </div>
                            </div>
                            <p class="text-sm text-blue-600 mb-3">${task.items}</p>
                            <div class="space-y-2">
                                ${task.checklist.map(item => {
                                    if (item.type === 'check') {
                                        return `<div class="flex items-center text-sm text-gray-600">
                                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                                            ${item.text}
                                        </div>`;
                                    } else if (item.type === 'link') {
                                        return `<div class="flex items-center text-sm">
                                            <i data-lucide="external-link" class="w-4 h-4 text-blue-500 mr-2"></i>
                                            <button class="text-blue-600 hover:underline" onclick="${item.action}('${item.data}')">${item.text}</button>
                                        </div>`;
                                    }
                                }).join('')}
                                ${task.suggestion ? `
                                    <div class="flex items-center text-sm mt-2">
                                        <i data-lucide="lightbulb" class="w-4 h-4 text-yellow-500 mr-2"></i>
                                        <button class="text-yellow-600 hover:underline" onclick="${task.suggestion.action}('${task.suggestion.data}')">${task.suggestion.text}</button>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });

            timeline.innerHTML = timelineHTML;

            // 更新导航按钮状态
            const prevBtn = document.getElementById('prev-recovery-day');
            const nextBtn = document.getElementById('next-recovery-day');

            prevBtn.disabled = currentRecoveryDay === 1;
            prevBtn.classList.toggle('opacity-50', currentRecoveryDay === 1);

            if (currentRecoveryDay === 3) {
                nextBtn.textContent = '完成复食';
                nextBtn.innerHTML = '完成复食<i data-lucide="check" class="inline-block -mt-1 ml-1"></i>';
            } else {
                nextBtn.innerHTML = '下一天<i data-lucide="chevron-right" class="inline-block -mt-1 ml-1"></i>';
            }

            // 重新初始化图标
            lucide.createIcons();
        }

        // 切换复食天数
        function changeRecoveryDay(direction) {
            if (direction === -1 && currentRecoveryDay > 1) {
                currentRecoveryDay--;
                updateRecoveryPhase();
            } else if (direction === 1) {
                if (currentRecoveryDay < 3) {
                    currentRecoveryDay++;
                    updateRecoveryPhase();
                } else {
                    // 完成复食阶段
                    showScreen('recovery-complete');
                }
            }
        }

        // 显示功法说明
        function showExerciseInfo(type) {
            const exerciseData = {
                qingjing: {
                    title: '早读《清静经》三遍',
                    content: `
                        <div class="space-y-4">
                            <p class="text-gray-700">《清静经》是道教重要经典，晨读有助于静心养神。</p>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">经文要点：</h4>
                                <ul class="text-sm text-blue-700 space-y-1">
                                    <li>• 清静无为，道之根本</li>
                                    <li>• 心静则神清，神清则智明</li>
                                    <li>• 读经时保持专注，心无杂念</li>
                                </ul>
                            </div>
                            <a href="#" class="inline-flex items-center text-blue-600 hover:underline">
                                <i data-lucide="external-link" class="w-4 h-4 mr-1"></i>
                                查看完整经文
                            </a>
                        </div>
                    `
                },
                yangsheng: {
                    title: '日用养生功法',
                    content: `
                        <div class="space-y-4">
                            <p class="text-gray-700">日用养生功法是适合日常练习的保健功法。</p>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800 mb-2">功法要点：</h4>
                                <ul class="text-sm text-green-700 space-y-1">
                                    <li>• 动作缓慢，配合呼吸</li>
                                    <li>• 意念集中，心神合一</li>
                                    <li>• 循序渐进，持之以恒</li>
                                </ul>
                            </div>
                            <a href="#" class="inline-flex items-center text-blue-600 hover:underline">
                                <i data-lucide="play" class="w-4 h-4 mr-1"></i>
                                观看教学视频
                            </a>
                        </div>
                    `
                },
                xingjiao: {
                    title: '空腹行脚功',
                    content: `
                        <div class="space-y-4">
                            <p class="text-gray-700">空腹行脚功是在空腹状态下进行的步行功法。</p>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-purple-800 mb-2">练习要点：</h4>
                                <ul class="text-sm text-purple-700 space-y-1">
                                    <li>• 空腹状态下进行</li>
                                    <li>• 步伐轻盈，呼吸自然</li>
                                    <li>• 时间控制在15-30分钟</li>
                                </ul>
                            </div>
                            <a href="#" class="inline-flex items-center text-blue-600 hover:underline">
                                <i data-lucide="play" class="w-4 h-4 mr-1"></i>
                                观看教学视频
                            </a>
                        </div>
                    `
                }
            };

            const data = exerciseData[type];
            if (data) {
                document.getElementById('exercise-title').textContent = data.title;
                document.getElementById('exercise-content').innerHTML = data.content;
                toggleModal('exercise-modal');
            }
        }

        // 显示饮食建议
        function showMealSuggestion(type) {
            const mealData = {
                breakfast: {
                    title: '早餐建议',
                    content: `
                        <div class="space-y-4">
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-yellow-800 mb-2">推荐搭配：</h4>
                                <ul class="text-sm text-yellow-700 space-y-1">
                                    <li>• 小米粥 + 蒸蛋 + 青菜</li>
                                    <li>• 燕麦粥 + 坚果 + 水果</li>
                                    <li>• 全麦面包 + 牛奶 + 香蕉</li>
                                </ul>
                            </div>
                            <div class="bg-red-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-red-800 mb-2">注意事项：</h4>
                                <ul class="text-sm text-red-700 space-y-1">
                                    <li>• 避免油腻、辛辣食物</li>
                                    <li>• 控制食量，七分饱即可</li>
                                    <li>• 细嚼慢咽，充分消化</li>
                                </ul>
                            </div>
                        </div>
                    `
                },
                lunch: {
                    title: '午餐建议',
                    content: `
                        <div class="space-y-4">
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-yellow-800 mb-2">推荐搭配：</h4>
                                <ul class="text-sm text-yellow-700 space-y-1">
                                    <li>• 蒸蛋羹 + 青菜汤 + 小馒头</li>
                                    <li>• 瘦肉粥 + 蒸蔬菜 + 水果</li>
                                    <li>• 鱼汤 + 米饭 + 清炒时蔬</li>
                                </ul>
                            </div>
                            <div class="bg-red-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-red-800 mb-2">注意事项：</h4>
                                <ul class="text-sm text-red-700 space-y-1">
                                    <li>• 以清淡为主，少油少盐</li>
                                    <li>• 适量蛋白质，促进恢复</li>
                                    <li>• 餐后适当休息</li>
                                </ul>
                            </div>
                        </div>
                    `
                },
                dinner: {
                    title: '晚餐建议',
                    content: `
                        <div class="space-y-4">
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-yellow-800 mb-2">推荐搭配：</h4>
                                <ul class="text-sm text-yellow-700 space-y-1">
                                    <li>• 蔬菜汤 + 蒸蛋 + 小米粥</li>
                                    <li>• 清蒸鱼 + 青菜 + 红薯</li>
                                    <li>• 豆腐汤 + 时蔬 + 杂粮饭</li>
                                </ul>
                            </div>
                            <div class="bg-red-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-red-800 mb-2">注意事项：</h4>
                                <ul class="text-sm text-red-700 space-y-1">
                                    <li>• 晚餐要清淡，易消化</li>
                                    <li>• 控制食量，避免过饱</li>
                                    <li>• 睡前2小时内不再进食</li>
                                </ul>
                            </div>
                        </div>
                    `
                }
            };

            const data = mealData[type];
            if (data) {
                document.getElementById('meal-title').textContent = data.title;
                document.getElementById('meal-content').innerHTML = data.content;
                toggleModal('meal-modal');
            }
        }

        // 显示合并任务详情
        function showMergedTaskDetail(timeSlot) {
            const data = fastingData[selectedPackage];
            const dayData = data.days[currentDay - 1];

            // 找到该时间段的所有任务
            const tasksInTimeSlot = dayData.tasks.filter(task => task.time === timeSlot);

            if (tasksInTimeSlot.length === 0) return;

            // 更新标题
            document.getElementById('task-detail-title').textContent = `${timeSlot} 任务详情`;

            // 生成任务详情内容
            let contentHTML = '';

            tasksInTimeSlot.forEach((task, index) => {
                const taskNumber = index + 1;

                contentHTML += `
                    <div class="mb-8 ${index > 0 ? 'border-t pt-6' : ''}">
                        <div class="mb-4">
                            <h2 class="text-xl font-bold text-gray-800">${task.name}</h2>
                            <p class="text-sm text-gray-500 mt-1">${task.period}</p>
                        </div>

                        ${task.items ? `
                        <div class="mb-4">
                            <h3 class="font-semibold text-gray-700 mb-2">所需物品/食谱</h3>
                            <div class="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-400">
                                <p class="text-gray-700">${task.items}</p>
                            </div>
                        </div>
                        ` : ''}

                        <div class="mb-4">
                            <h3 class="font-semibold text-gray-700 mb-3">任务清单</h3>
                            <div class="space-y-3">
                                ${task.checklist.map((item, itemIndex) => {
                                    // 判断是否为视频任务
                                    const isVideoTask = item.includes('视频') || item.includes('——');
                                    const hasVideoLink = item.includes('视频链接') || item.includes('视频《') || task.hasVideo;

                                    if (isVideoTask && hasVideoLink) {
                                        return `
                                            <div class="bg-emerald-50 p-4 rounded-lg border border-emerald-200">
                                                <div class="flex items-start">
                                                    <input type="checkbox" class="h-5 w-5 rounded text-emerald-600 focus:ring-emerald-500 mt-0.5 mr-3">
                                                    <div class="flex-1">
                                                        <p class="text-gray-700 mb-2">${item}</p>
                                                        <div class="bg-white p-3 rounded border">
                                                            <video class="w-full h-auto rounded" controls poster="https://placehold.co/400x225/d1fae5/10b981?text=${encodeURIComponent(task.videoTitle || '功法视频')}">
                                                                <source src="" type="video/mp4">
                                                                您的浏览器不支持视频播放。
                                                            </video>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        `;
                                    } else {
                                        return `
                                            <label class="flex items-start p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                                <input type="checkbox" class="h-5 w-5 rounded text-emerald-600 focus:ring-emerald-500 mt-0.5 mr-3">
                                                <span class="text-gray-700 flex-1">${item}</span>
                                            </label>
                                        `;
                                    }
                                }).join('')}
                            </div>
                        </div>
                    </div>
                `;
            });

            // 更新任务详情内容
            const taskDetailContent = document.getElementById('task-detail-content');
            taskDetailContent.innerHTML = `<div class="space-y-6">${contentHTML}</div>`;

            // 重新初始化图标
            lucide.createIcons();

            // 显示任务详情页面
            showScreen('task-detail');
        }

        // 保留原有的单任务详情函数（兼容性）
        function showTaskDetail(taskIndex = 0) {
            const data = fastingData[selectedPackage];
            const dayData = data.days[currentDay - 1];
            const task = dayData.tasks[taskIndex];

            if (!task) return;

            showMergedTaskDetail(task.time);
        }

        // 提交打卡
        function submitCheckin() {
            const data = fastingData[selectedPackageType];
            const totalDays = data.days.length;

            toggleModal('checkin-modal');

            // 检查是否是最后一天
            if (currentDay === totalDays) {
                // 最后一天打卡完成，进入最终体重记录页面
                showScreen('final-weight-info');
            } else {
                // 普通打卡完成
                alert('打卡完成！');
            }
        }

        // 完成任务
        function completeTask() {
            // 推进到下一个任务
            const data = fastingData[selectedPackage];
            const dayData = data.days[currentDay - 1];

            if (currentTaskIndex < dayData.tasks.length - 1) {
                currentTaskIndex++;
            } else {
                // 当天任务完成，可以进入下一天或完成整个流程
                if (currentDay < data.days.length) {
                    currentDay++;
                    currentTaskIndex = 0;
                } else {
                    // 所有任务完成，跳转到成果页面
                    showScreen('results');
                    return;
                }
            }

            showScreen('dashboard');
        }

        // 更新成果页面
        function updateResults() {
            const data = fastingData[selectedPackage];
            document.getElementById('results-title').textContent = `恭喜您，完成${selectedPackage}日轻断食！`;
            
            // 根据天数调整减重效果
            const weightLoss = selectedPackage === '2' ? 2.0 : 2.5;
            const finalWeight = 65.5 - weightLoss;
            
            document.getElementById('final-weight').innerHTML = `${finalWeight.toFixed(1)} <span class="text-base font-medium">kg</span>`;
            document.getElementById('weight-loss').innerHTML = `-${weightLoss.toFixed(1)} <span class="text-base font-medium">kg</span>`;
        }

        // 初始化复食阶段
        updateRecoveryPhase();

        // 初始化日期选择
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认开始日期为今天
            const today = new Date();
            const todayString = today.toISOString().split('T')[0];
            const startDateInput = document.getElementById('start-date');
            if (startDateInput) {
                startDateInput.value = todayString;
                startDateInput.addEventListener('change', updateDateRange);
                updateDateRange(); // 初始化显示

                // 延迟启动倒计时，确保DOM已完全加载
                setTimeout(startCountdownTimer, 500);
            }
        });

        // 默认显示第一个页面
        showScreen('intro');
    </script>
</body>
</html>