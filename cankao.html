<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轻断食 App 页面原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <style>
        body {
            font-family: 'Inter', 'Helvetica Neue', 'Hiragino Sans GB', 'WenQuanYi Micro Hei', 'Microsoft Yahei', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .screen-container {
            max-width: 420px; /* 模拟手机屏幕宽度 */
            min-height: 800px;
            margin: 2rem auto;
            border: 8px solid #1a202c;
            border-radius: 40px;
            box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
            overflow: hidden;
            position: relative;
        }
        .screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            pointer-events: none;
            overflow-y: auto;
            background-color: #f8fafc; /* 默认背景色 */
        }
        .screen.active {
            opacity: 1;
            pointer-events: auto;
        }
        /* 自定义滚动条 */
        .screen::-webkit-scrollbar {
            width: 4px;
        }
        .screen::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }
        .screen::-webkit-scrollbar-track {
            background: transparent;
        }
        .nav-button.active {
            background-color: #10b981;
            color: white;
            font-weight: 600;
        }
        .timeline-item .content {
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .timeline-item .content:hover {
            background-color: #f0fdf4;
        }
    </style>
</head>
<body class="bg-gray-100">

    <div class="p-4 text-center">
        <h1 class="text-2xl font-bold text-gray-800">轻断食App页面设计原型</h1>
        <p class="text-gray-600 mt-2">请点击下方按钮切换查看不同页面。原型基于移动端竖屏设计。</p>
    </div>

  

    <!-- 手机屏幕模拟容器 -->
    <div class="screen-container bg-white">
        <!-- 屏幕内容 -->
        <div id="intro" class="screen">

            <!-- 4. 每日任务主页 -->
            <div class="p-6 bg-white rounded-b-3xl shadow-sm">
                <div class="flex justify-between items-center">
                    <button class="p-2 rounded-full hover:bg-gray-100"><i data-lucide="user-circle-2"></i></button>
                    <h1 class="text-xl font-bold text-gray-800">轻断食 · 第一天</h1>
                    <button class="p-2 rounded-full hover:bg-gray-100"><i data-lucide="help-circle"></i></button>
                </div>
                <div class="mt-6 bg-emerald-600 text-white p-5 rounded-xl shadow-lg shadow-emerald-200">
                    <p class="text-sm opacity-80">当前任务 (07:00 - 09:00)</p>
                    <h2 class="text-2xl font-bold mt-1">辰时 · 排肠</h2>
                    <p class="text-sm mt-2 opacity-80">所需物品：君仓清50-60g</p>
                    <button class="w-full mt-4 bg-white text-emerald-700 font-bold py-3 rounded-lg hover:bg-emerald-50 transition-all" onclick="showScreen('task-detail-drainage')">
                        开始排肠
                    </button>
                </div>
            </div>
            
            <div class="p-6 space-y-4">
                <h3 class="font-semibold text-gray-700">今日全天安排</h3>
                <!-- Timeline -->
                <div class="relative border-l-2 border-gray-200 ml-3">
                    <!-- Task Item: 晨功 -->
                    <div class="timeline-item mb-6 ml-8">
                        <span class="absolute flex items-center justify-center w-6 h-6 bg-green-200 rounded-full -left-3 ring-4 ring-white">
                            <i data-lucide="check" class="w-4 h-4 text-green-700"></i>
                        </span>
                        <div class="content p-4 rounded-lg bg-white shadow-sm" onclick="showScreen('task-detail-morning')">
                            <p class="text-sm text-gray-500">05:00 - 07:00</p>
                            <p class="font-semibold text-gray-800">卯时 · 晨功</p>
                            <p class="text-xs text-gray-400 mt-1">所需: 艾灸贴4贴</p>
                        </div>
                    </div>
                    <!-- Task Item: 排肠 (Current) -->
                    <div class="timeline-item mb-6 ml-8">
                        <span class="absolute flex items-center justify-center w-6 h-6 bg-emerald-200 rounded-full -left-3 ring-4 ring-white">
                            <i data-lucide="droplets" class="w-4 h-4 text-emerald-700"></i>
                        </span>
                        <div class="content p-4 rounded-lg bg-white shadow-sm border border-emerald-300" onclick="showScreen('task-detail-drainage')">
                            <p class="text-sm text-gray-500">07:00 - 09:00</p>
                            <p class="font-semibold text-emerald-800">辰时 · 排肠</p>
                            <p class="text-xs text-gray-400 mt-1">所需: 君仓清50-60g</p>
                        </div>
                    </div>
                    <!-- Task Item: 午斋课 -->
                    <div class="timeline-item mb-6 ml-8">
                        <span class="absolute flex items-center justify-center w-6 h-6 bg-gray-200 rounded-full -left-3 ring-4 ring-white">
                            <i data-lucide="wind" class="w-4 h-4 text-gray-500"></i>
                        </span>
                        <div class="content p-4 rounded-lg">
                            <p class="text-sm text-gray-500">11:00 - 12:00</p>
                            <p class="font-semibold text-gray-600">午时 · 午斋课</p>
                            <p class="text-xs text-gray-400 mt-1">功法: 服气吞津</p>
                        </div>
                    </div>
                    <!-- Task Item: 下午茶 -->
                    <div class="timeline-item mb-6 ml-8">
                        <span class="absolute flex items-center justify-center w-6 h-6 bg-gray-200 rounded-full -left-3 ring-4 ring-white">
                            <i data-lucide="coffee" class="w-4 h-4 text-gray-500"></i>
                        </span>
                        <div class="content p-4 rounded-lg">
                            <p class="text-sm text-gray-500">15:00 - 17:00</p>
                            <p class="font-semibold text-gray-600">申时 · 扶阳下午茶</p>
                            <p class="text-xs text-gray-400 mt-1">所需: 丹道药茶/回春丹</p>
                        </div>
                    </div>
                    <!-- Task Item: 肝胆排毒 -->
                    <div class="timeline-item mb-6 ml-8">
                        <span class="absolute flex items-center justify-center w-6 h-6 bg-gray-200 rounded-full -left-3 ring-4 ring-white">
                            <i data-lucide="moon" class="w-4 h-4 text-gray-500"></i>
                        </span>
                        <div class="content p-4 rounded-lg">
                            <p class="text-sm text-gray-500">21:00 - 22:00</p>
                            <p class="font-semibold text-gray-600">亥时 · 肝胆排毒</p>
                            <p class="text-xs text-gray-400 mt-1">所需: 肝胆排毒液</p>
                        </div>
                    </div>
                </div>
            </div>
             <div class="h-10"></div> <!-- 增加底部空间 -->
        </div>

        <div id="task-detail-morning" class="screen">
            <!-- 5. 任务详情页 (晨功) -->
            <div class="relative">
                <img src="https://placehold.co/420x240/d1fae5/10b981?text=功法视频" class="w-full h-auto">
                <button onclick="showScreen('dashboard')" class="absolute top-4 left-4 p-2 bg-white/70 rounded-full backdrop-blur-sm"><i data-lucide="arrow-left"></i></button>
                <div class="absolute inset-0 flex items-center justify-center">
                    <button class="p-6 bg-white/30 rounded-full backdrop-blur-sm"><i data-lucide="play" class="w-10 h-10 text-white fill-white"></i></button>
                </div>
            </div>
            <div class="p-6">
                <p class="text-sm text-emerald-600 font-medium">05:00 - 07:00</p>
                <h1 class="text-3xl font-bold text-gray-800 mt-1">卯时 · 晨功</h1>
                <div class="mt-6 space-y-4 text-gray-700">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-emerald-100 text-emerald-700 rounded-full flex items-center justify-center font-bold">1</div>
                        <p class="ml-4">喝一杯温水。</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-emerald-100 text-emerald-700 rounded-full flex items-center justify-center font-bold">2</div>
                        <p class="ml-4">日用养生功法：扣齿、饶舌、揉耳、搓面、揉肚脐等，顺逆时针各108下。</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-emerald-100 text-emerald-700 rounded-full flex items-center justify-center font-bold">3</div>
                        <p class="ml-4">早读《清静经》三遍。</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-emerald-100 text-emerald-700 rounded-full flex items-center justify-center font-bold">4</div>
                        <p class="ml-4">艾灸/艾灸贴：贴敷太渊穴、关元穴和足三里。（点击查看穴位图）</p>
                    </div>
                     <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-emerald-100 text-emerald-700 rounded-full flex items-center justify-center font-bold">5</div>
                        <p class="ml-4">练习吞津服气功法（9/18/27个）。</p>
                    </div>
                     <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-emerald-100 text-emerald-700 rounded-full flex items-center justify-center font-bold">6</div>
                        <p class="ml-4">运动升阳：空腹行脚功。</p>
                    </div>
                </div>
            </div>
            <div class="p-6 sticky bottom-0 bg-white border-t">
                <button class="w-full bg-emerald-600 text-white font-bold py-4 rounded-xl shadow-lg shadow-emerald-200 hover:bg-emerald-700 transition-all">
                    <i data-lucide="check-circle" class="inline-block -mt-1 mr-2"></i>完成打卡
                </button>
            </div>
        </div>
        
        <div id="task-detail-drainage" class="screen">
            <!-- 6. 任务详情页 (排肠) -->
            <div class="relative">
                <img src="https://placehold.co/420x240/e0f2fe/0ea5e9?text=课程视频" class="w-full h-auto">
                <button onclick="showScreen('dashboard')" class="absolute top-4 left-4 p-2 bg-white/70 rounded-full backdrop-blur-sm"><i data-lucide="arrow-left"></i></button>
                <div class="absolute inset-0 flex items-center justify-center">
                    <button class="p-6 bg-white/30 rounded-full backdrop-blur-sm"><i data-lucide="play" class="w-10 h-10 text-white fill-white"></i></button>
                </div>
            </div>
            <div class="p-6">
                <p class="text-sm text-cyan-600 font-medium">07:00 - 09:00</p>
                <h1 class="text-3xl font-bold text-gray-800 mt-1">辰时 · 排肠</h1>
                <div class="mt-6 space-y-4 text-gray-700">
                    <div class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
                        <h3 class="font-semibold text-blue-800">准备工作</h3>
                        <p class="text-sm text-blue-700 mt-1">准备30℃温水约2500ml，君仓清50-60g。</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-cyan-100 text-cyan-700 rounded-full flex items-center justify-center font-bold">1</div>
                        <p class="ml-4">将君仓清倒入温水中，充分摇匀配制成排肠液。</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-cyan-100 text-cyan-700 rounded-full flex items-center justify-center font-bold">2</div>
                        <p class="ml-4">在1小时内，分多次将排肠液全部饮用完毕。</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-cyan-100 text-cyan-700 rounded-full flex items-center justify-center font-bold">3</div>
                        <p class="ml-4">饮用期间，可跟随视频进行简单拉伸，促进肠道蠕动。</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-cyan-100 text-cyan-700 rounded-full flex items-center justify-center font-bold">4</div>
                        <p class="ml-4">观看视频课程《健康从“肠”计议》，了解肠道健康知识。</p>
                    </div>
                </div>
            </div>
            <div class="p-6 sticky bottom-0 bg-white border-t">
                <button class="w-full bg-cyan-600 text-white font-bold py-4 rounded-xl shadow-lg shadow-cyan-200 hover:bg-cyan-700 transition-all">
                    <i data-lucide="check-circle" class="inline-block -mt-1 mr-2"></i>完成打卡
                </button>
            </div>
        </div>

        <div id="refeed" class="screen p-6 bg-gray-50">
            <!-- 7. 复食计划页 -->
            <h1 class="text-2xl font-bold text-gray-800 text-center mt-4">复食 · 第一天</h1>
            <p class="text-gray-600 text-center mb-6">循序渐进，让肠胃温柔重启</p>

            <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg mb-6">
                <div class="flex">
                    <div class="py-1"><i data-lucide="alert-triangle" class="h-6 w-6 text-red-500"></i></div>
                    <div class="ml-3">
                        <h3 class="font-semibold text-red-800">复食核心要点</h3>
                        <p class="text-sm text-red-700 mt-1">🚫 不可放油盐！切勿吃饱，不饿即可！忌生冷、辛辣、荤腥！</p>
                    </div>
                </div>
            </div>

            <div class="space-y-6">
                <div class="bg-white p-4 rounded-xl shadow-sm">
                    <p class="text-sm font-semibold text-emerald-700">早餐 07:00</p>
                    <div class="flex items-center mt-2">
                        <img src="https://placehold.co/100x100/f0fdf4/16a34a?text=小米粥" class="w-20 h-20 rounded-lg object-cover">
                        <div class="ml-4">
                            <p class="font-bold text-gray-800">小米粥或米糊</p>
                            <p class="text-sm text-gray-500 mt-1">餐前服用回春丹、滋阴丸各1颗（咀嚼36次以上）。</p>
                            <p class="text-xs text-gray-500 mt-1">尽量选用粗、软、流食。</p>
                        </div>
                    </div>
                </div>
                 <div class="bg-white p-4 rounded-xl shadow-sm">
                    <p class="text-sm font-semibold text-blue-700">午餐 11:00</p>
                    <div class="flex items-center mt-2">
                        <img src="https://placehold.co/100x100/ecfdf5/16a34a?text=山药粉" class="w-20 h-20 rounded-lg object-cover">
                        <div class="ml-4">
                            <p class="font-bold text-gray-800">山药粉或藕粉</p>
                            <p class="text-sm text-gray-500 mt-1">餐前服用回春丹1颗。</p>
                            <p class="text-xs text-gray-500 mt-1">可搭配少量煮烂的青菜、瓜汤。</p>
                        </div>
                    </div>
                </div>
                 <div class="bg-white p-4 rounded-xl shadow-sm">
                    <p class="text-sm font-semibold text-purple-700">晚餐 17:00</p>
                    <div class="flex items-center mt-2">
                        <img src="https://placehold.co/100x100/f5f3ff/8b5cf6?text=瓜汤" class="w-20 h-20 rounded-lg object-cover">
                        <div class="ml-4">
                            <p class="font-bold text-gray-800">瓜汤类</p>
                            <p class="text-sm text-gray-500 mt-1">餐前服用滋阴丸1颗。</p>
                            <p class="text-xs text-gray-500 mt-1">务必煮烂，少油少盐。</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="h-10"></div>
        </div>

        <div id="results" class="screen p-6 bg-gradient-to-b from-emerald-50 to-amber-50 flex flex-col justify-center text-center">
            <!-- 8. 成果总结页 -->
            <div class="flex-grow flex flex-col items-center justify-center">
                <i data-lucide="party-popper" class="w-16 h-16 text-amber-500"></i>
                <h1 class="text-3xl font-bold text-gray-800 mt-4">恭喜您，完成3日轻断食！</h1>
                <p class="text-gray-600 mt-2">这是一次了不起的坚持，为自己喝彩！</p>
                
                <div class="mt-8 bg-white/70 backdrop-blur-sm p-6 rounded-2xl shadow-md w-full">
                    <h3 class="font-semibold text-gray-800">您的成果报告</h3>
                    <div class="mt-4 grid grid-cols-2 gap-4">
                        <div class="bg-emerald-100 p-4 rounded-lg">
                            <p class="text-sm text-emerald-800">体重变化</p>
                            <p class="text-2xl font-bold text-emerald-600">-2.5 <span class="text-base font-medium">kg</span></p>
                        </div>
                        <div class="bg-emerald-100 p-4 rounded-lg">
                            <p class="text-sm text-emerald-800">腰围变化</p>
                            <p class="text-2xl font-bold text-emerald-600">-3 <span class="text-base font-medium">cm</span></p>
                        </div>
                        <div class="bg-gray-100 p-4 rounded-lg col-span-2">
                            <p class="text-sm text-gray-800">累计打卡</p>
                            <p class="text-2xl font-bold text-gray-600">21 <span class="text-base font-medium">次</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="py-6">
                <button class="w-full bg-emerald-600 text-white font-bold py-4 rounded-xl shadow-lg shadow-emerald-200 hover:bg-emerald-700 transition-all">
                    <i data-lucide="share-2" class="inline-block -mt-1 mr-2"></i>生成分享海报
                </button>
                <button class="w-full mt-3 text-gray-600 font-medium py-3 rounded-lg hover:bg-gray-200 transition-all" onclick="showScreen('intro')">
                    返回首页
                </button>
            </div>
        </div>
    </div>

</body>
</html>
